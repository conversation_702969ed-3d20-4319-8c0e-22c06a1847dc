.upload-image-card-container {
  position relative
  display flex
  flex-direction column
  justify-content flex-start
  box-sizing border-box
  width 100%
  margin-bottom 20rem
  overflow hidden
  border 1rem solid var(--border-color)
  border-radius 6rem

  &.wait-upload {
    border-color var(--await-upload-color)
  }

  &.uploading {
    border-color var(--uploading-color)
  }

  &.uploaded {
    border-color var(--uploaded-color)
  }

  &:last-child {
    margin-bottom 0
  }


  &:hover {
    .del-img-btn {
      display block
    }
  }


  .img-show-container {
    position relative
    box-sizing border-box
    width 100%
    height 140rem

    .el-image {
      width 100%
      height 100%
    }
  }


  .before-upload-handle-container {
    position relative
    box-sizing border-box
    width 100%
    border-top 1rem solid var(--border-color)

    .img-name-box {
      position relative
      display flex
      align-items center
      justify-content space-between
      box-sizing border-box
      width 100%
      padding 2rem 5rem
      font-size 13rem
      border-bottom 1rem solid var(--border-color)

      &.no-border {
        border-bottom none
      }

      .img-name {
        position relative
        box-sizing border-box
        width calc(100% - 20rem)
      }

      .fold-btn {
        position relative
        display flex
        align-items center
        justify-content end
        box-sizing border-box
        width 20rem
        font-size 15rem
        cursor pointer
      }
    }

    .img-name-operate-box {
      position relative
      display flex
      flex-direction column
      justify-content center
      box-sizing border-box
      padding 2rem 5rem
      border-bottom 1rem solid var(--border-color)

      &.folded {
        display none
      }


      .operate-item {
        display flex
        align-items center
        height 28rem

        .rename-input {
          margin-left 10rem
        }
      }
    }

    .img-info-box {
      display flex
      align-items center
      justify-content space-between
      padding 5rem
      font-size 12rem
      user-select none

      .file-size-box {
        transform scale(0.9)

        .file-size-item {
          padding 2rem 3rem
          background var(--background-color-3)
          border-radius 3rem
        }


        .original-file-size {
          margin-left -4rem

          &.del-line {
            text-decoration line-through
          }
        }


        .finial-file-size {
          margin-left 6rem
          color var(--el-color-primary)
        }
      }
    }
  }


  .after-upload-handle-container {
    position relative
    box-sizing border-box
    width 100%
    height 30rem
    color var(--el-color-primary)
    font-size 13rem
    background var(--el-color-primary-light-9)
    border-top 1rem solid var(--border-color)
    cursor pointer

    &:hover {
      color var(--el-color-white)
      background var(--el-color-primary)
    }
  }


  .del-img-btn {
    position absolute
    top 6rem
    right 6rem
    display none
    color var(--background-color)
    font-size 22rem
    cursor pointer
  }

  .upload-status-box {
    position absolute
    top -8rem
    left -16rem
    box-sizing border-box
    width 46rem
    height 26rem
    color #fff
    text-align center
    box-shadow 0 1rem 1rem var(--border-color)
    transform rotate(315deg)

    &.wait-upload {
      background var(--await-upload-color)
    }

    &.uploaded {
      background var(--uploaded-color)

      .el-icon {
        margin-top 12rem
      }
    }

    .el-icon {
      margin-top 10rem
      font-size 12rem
      transform rotate(45deg)
    }
  }
}
