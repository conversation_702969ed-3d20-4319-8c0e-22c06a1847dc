.upload-page-container {
  display flex
  justify-content space-between
  width 100%
  height 100%

  .upload-page-left {
    flex-shrink 0
    box-sizing border-box
    width 300rem
    height 100%
    margin-right 10rem
    border-top-right-radius 0
    border-bottom-right-radius 0

    .uploaded-item {
      width 100%
      margin-bottom 20rem

      &:last-child {
        margin-bottom 0
      }
    }
  }


  .upload-page-right {
    box-sizing border-box
    width 100%
    height 100%
    overflow-y auto

    &.has-left {
      border-top-left-radius 0
      border-bottom-left-radius 0
    }

    .row-item {
      display flex
      justify-content center
      box-sizing border-box
      width 100%
      margin-bottom 16rem

      &:last-child {
        margin-bottom 0
      }

      .content-box {
        box-sizing border-box
        width 100%
        max-width $content-max-width
        margin 0 auto

        &.upload-area-status {
          display flex
          align-items center
          justify-content space-between
          margin-bottom 10rem
          font-size 14rem
        }


        &.operation-btn {
          display flex
          justify-content flex-end
        }


        .shortcut-key {
          margin-left 5rem
          padding 4rem 5rem
          font-size 12rem
          letter-spacing 1rem
          border-radius 4rem
          box-shadow 1rem 2rem 3rem var(--shadow-color)

          +picx-tablet() {
            display none
          }
        }
      }
    }

    .upload-tools {
      width 100%

      .repos-dir-info {
        margin-bottom 20rem
        font-size 12rem

        .repos-dir-info-item {
          margin-right 10rem

          &:last-child {
            margin-right 0
          }
        }
      }
    }
  }
}
