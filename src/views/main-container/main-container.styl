$layout-padding = $left-side-padding * 0.8
$header-margin-bottom = $left-side-padding * 0.5

.main-container {
  position absolute
  display flex
  flex-direction column
  justify-content space-between
  box-sizing border-box
  width 100%
  height 100%
  background var(--background-color-2)

  .top-container {
    flex-shrink 0
    width 100%
    height $header-height
    margin-bottom $header-margin-bottom
  }


  .bottom-container {
    display flex
    justify-content space-between
    width 100%
    height 'calc(100% - %s)' % ($header-height + $header-margin-bottom)


    .bottom-left-box {
      flex-shrink 0
      width $left-side-width
      height 100%

      &.folded {
        width $left-side-folded-width
      }
    }


    .bottom-right-box {
      width 100%
      height 100%
      padding-right $layout-padding
      overflow-x auto
    }
  }
}
