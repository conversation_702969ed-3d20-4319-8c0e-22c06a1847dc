$tool-panel-header-height = 30rem
$tool-item-height = 160rem
$tool-item-top-height = 58rem
$tool-item-padding = 20rem

.tool-panel {
  position relative
  box-sizing border-box
  width 100%
  height 100%

  .panel-header {
    position relative
    display flex
    align-items flex-start
    box-sizing border-box
    width 100%
    height $tool-panel-header-height
  }

  .panel-body {
    position relative
    box-sizing border-box
    width 100%
    height 'calc(100% - %s)' % $tool-panel-header-height
    overflow hidden
    border 1px solid var(--border-color)
    border-radius 8rem
  }
}

.toolbox {
  position relative
  display grid
  grid-gap 20rem
  grid-template-columns repeat(4, 1fr)
  box-sizing border-box
  padding 0

  +picx-tablet-2() {
    grid-template-columns repeat(3, 1fr)
  }

  +picx-tablet() {
    grid-template-columns repeat(2, 1fr)
  }

  +picx-mobile() {
    grid-template-columns repeat(1, 1fr)

    .tool-item {
      height $tool-item-height * 0.92 !important
      padding $tool-item-padding * 0.92 !important
    }
  }

  .tool-item {
    position relative
    box-sizing border-box
    height $tool-item-height
    padding $tool-item-padding
    border-radius 10rem
    box-shadow 0 1rem 3rem var(--shadow-color)
    transition all ease 0.2s

    &:hover {
      box-shadow 0 0 6rem var(--shadow-hover-color)
      transform translateY(-2%) scale(1.02)
      cursor pointer
    }

    .top {
      position relative
      display flex
      align-items center
      justify-content flex-start
      box-sizing border-box
      width 100%
      height $tool-item-top-height

      .left {
        position relative
        box-sizing border-box
        width $tool-item-top-height
        height $tool-item-top-height
        color var(--el-color-primary)
        background var(--el-color-primary-light-9)
        border-radius 10rem
      }

      .right {
        position relative
        box-sizing border-box
        padding-left 12rem
        color var(--text-color)
        font-weight 600
        font-size 20rem
      }
    }

    .bottom {
      position relative
      display flex
      align-items flex-end
      box-sizing border-box
      width 100%
      height 'calc(100% - %s)' % $tool-item-top-height
      color var(--text-color-4)
    }
  }
}
