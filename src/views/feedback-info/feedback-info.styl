.feedback-page-container {

  .help-info-item {
    display flex
    align-items center
    margin-bottom 10rem
    padding 6rem
    font-size 16rem

    code {
      box-sizing border-box
      padding 3rem 6rem
      font-weight bold
      background var(--background-color-2)
      border-radius 6rem
      box-shadow 2rem 2rem 5rem var(--shadow-color)
    }

    &:last-child {
      margin-bottom 0
    }
  }

  .description {
    font-weight bold
    line-height 28rem
  }

  .red-text {
    margin-top 6rem
    color var(--el-color-danger)
    font-size 22rem
  }
}
