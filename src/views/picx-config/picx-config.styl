.config-page-container {

  .row-item {
    position relative
    box-sizing border-box
    margin-bottom 26rem

    &:last-child {
      margin-bottom 0
    }


    &.token {
      display flex
      justify-content space-between

      .el-button {
        margin-left 20rem
      }
    }


    &.confirm-btn {
      float right
    }


    .cell-item {
      display flex
      align-items center

      .el-icon {
        margin-right 6rem
        font-size 18rem
      }
    }


    .repo-descriptions-item {
      display flex
      align-items center
      justify-content space-between
    }

    .dir-box {
      .dir-item {
        display flex
        align-items center
        justify-content space-between
        margin-bottom 8rem
      }
    }


    .refresh-icon {
      display flex
      align-items center
      justify-content flex-end
      font-size 20rem
      cursor pointer
    }
  }
}
