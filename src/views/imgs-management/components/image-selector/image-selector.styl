.selector-wrapper {
  display flex
  align-items center
  justify-content space-between
  box-sizing border-box
  width 100%
  padding 4rem 12rem
  border-bottom 1rem solid var(--background-color-3)


  .selector-left-box {
    display flex
    align-items center

    :deep(.el-checkbox) {
      font-weight unset
    }

    :deep(.el-checkbox__label ) {
      line-height unset
    }

    .cancel-select-btn {
      color var(--el-color-primary-light-3)
      font-size 13rem
      cursor pointer
    }

    div.item {
      margin-left 8rem
    }
  }


  .selector-right-box {
    .btn-icon {
      margin-left 10rem
      font-size 16rem
      cursor pointer
    }
  }
}
