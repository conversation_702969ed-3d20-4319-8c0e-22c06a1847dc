.header-content-box {
  display flex
  align-items center
  justify-content space-between
  width 100%
  height 100%
  padding-right 20rem


  .header-left {
    display flex
    justify-content flex-start
    width $left-side-width
    height 100%
    padding-left $left-side-padding
    user-select none

    .brand-box {
      display flex
      align-items center
      justify-content flex-start
      height 100%
      cursor pointer

      .fold-icon {
        margin-right $left-side-padding * 0.8
        font-size 28rem
        cursor pointer
      }

      .logo {
        width 40rem
        height 40rem
        margin-right 10rem

        img {
          width 100%
          height 100%
        }
      }


      .title {
        font-weight bold
        font-size 30rem
      }
    }
  }


  .header-right {
    display flex
    align-items center
    justify-content flex-end

    .avatar {
      margin-left 6rem
    }
  }
}
