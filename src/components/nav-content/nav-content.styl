.nav-content-container {
  display flex
  flex-direction column
  justify-content space-between
  width 100%
  height 100%
  padding-top $left-side-padding
  padding-right $left-side-padding
  padding-bottom $left-side-padding


  &.folded {
    padding-left $left-side-padding

    .top-box {
      .nav-menu-item {
        justify-content center
        height $left-side-folded-width - $left-side-padding * 2
        margin-bottom 8rem
        padding 0
        border-radius $box-border-radius

        .nav-name {
          display none
        }
      }
    }


    .bottom-box {
      justify-content space-between
      padding-left 0

      .fold-icon {
        font-size 20rem
      }

      .site-count {
        display none
      }
    }
  }


  .top-box {
    .nav-menu-item {
      display flex
      align-items center
      justify-content flex-start
      width 100%
      margin-bottom 4rem
      padding-top $left-side-padding * 0.6
      padding-bottom $left-side-padding * 0.6
      padding-left $left-side-padding
      border-top-right-radius $box-border-radius
      border-bottom-right-radius $box-border-radius
      cursor pointer
      user-select none

      &:hover {
        color var(--el-color-primary)
        background var(--el-color-primary-light-9)
      }


      &.active {
        color var(--el-color-primary)
        font-weight bold
        background var(--el-color-primary-light-9)
      }


      .nav-name {
        margin-left $left-side-padding * 0.5
        font-size 14rem
      }
    }
  }


  .bottom-box {
    display flex
    align-items center
    justify-content space-between
    padding-left $left-side-padding

    .fold-icon {
      font-size 18rem
      cursor pointer
    }
  }
}
