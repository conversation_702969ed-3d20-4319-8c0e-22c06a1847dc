.getting-images-container {
  position relative
  z-index 999
  display flex
  align-items center
  justify-content center
  box-sizing border-box
  width 100%
  height 300rem
  border 4rem dashed var(--text-color-4)
  border-radius 8rem

  &.focus {
    border-color var(--el-color-primary)
  }

  &.disabled {
    pointer-events none
  }

  &:hover {
    border-color var(--el-color-primary)
  }

  label {
    position absolute
    z-index 1000
    display block
    width 100%
    height 100%
    cursor pointer
  }

  input[type="file"] {
    position absolute
    top -9999rem
    left -9999rem
  }

  .upload-area-tips {
    color #aaa
    text-align center
    user-select none

    .icon {
      font-size 100rem
    }

    .text {
      font-size 20rem
      cursor default
    }
  }


  .preview-img {
    width 100%
    height 100%
    object-fit cover
  }
}
