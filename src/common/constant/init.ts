export const PICX_REPO_NAME = 'XPoet/picx'
export const INIT_REPO_NAME = 'picx-images-hosting'
export const INIT_REPO_DESC = 'PicX images hosting repository'
export const INIT_REPO_BARNCH = 'master'
export const GH_PAGES = 'gh-pages'
export const PICX_UPLOAD_IMG_DESC = 'Upload image via PicX (https://github.com/XPoet/picx)'
export const PICX_UPLOAD_IMGS_DESC = 'Upload images via PicX (https://github.com/XPoet/picx)'
export const PICX_DEL_IMG_DESC = 'Delete image via PicX (https://github.com/XPoet/picx)'
export const PICX_INIT_SETTINGS_MSG = 'Init settings via PicX (https://github.com/XPoet/picx)'
export const PICX_UPDATE_SETTINGS_MSG = 'Update settings via PicX (https://github.com/XPoet/picx)'
export const PICX_INIT_REPO_MSG = 'Init repo via PicX (https://github.com/XPoet/picx)'

export const PICX_INIT_DEPLOY_MSG = 'Init deploy status via PicX (https://github.com/XPoet/picx)'
export const PICX_UPDATE_DEPLOY_MSG =
  'Update deploy status via PicX (https://github.com/XPoet/picx)'
