/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AuthorizationStatusBar: typeof import('./components/authorization-status-bar/authorization-status-bar.vue')['default']
    Base64Tool: typeof import('./components/tools/base64-tool/base64-tool.vue')['default']
    CloudSettingsBar: typeof import('./components/cloud-settings-bar/cloud-settings-bar.vue')['default']
    CompressConfigBox: typeof import('./components/compress-config-box/compress-config-box.vue')['default']
    CompressTool: typeof import('./components/tools/compress-tool/compress-tool.vue')['default']
    CopyImageLink: typeof import('@/views/imgs-management/components/copy-image-link/copy-image-link.vue')['default']
    CopySourceRepo: typeof import('./components/copy-source-repo/copy-source-repo.vue')['default']
    DeployBar: typeof import('./components/deploy-status-bar/deploy-bar.vue')['default']
    DeployStatusBar: typeof import('./components/deploy-status-bar/deploy-status-bar.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    FolderCard: typeof import('./components/folder-card/folder-card.vue')['default']
    GettingImages: typeof import('./components/getting-images/getting-images.vue')['default']
    GlobalSettings: typeof import('./components/global-settings/global-settings.vue')['default']
    HeaderContent: typeof import('./components/header-content/header-content.vue')['default']
    IEpAim: typeof import('~icons/ep/aim')['default']
    IEpArrowLeftBold: typeof import('~icons/ep/arrow-left-bold')['default']
    IEpArrowRightBold: typeof import('~icons/ep/arrow-right-bold')['default']
    IEpBell: typeof import('~icons/ep/bell')['default']
    IEpCaretBottom: typeof import('~icons/ep/caret-bottom')['default']
    IEpCaretLeft: typeof import('~icons/ep/caret-left')['default']
    IEpCaretRight: typeof import('~icons/ep/caret-right')['default']
    IEpCheck: typeof import('~icons/ep/check')['default']
    IEpChecked: typeof import('~icons/ep/checked')['default']
    IEpCircleCheckFilled: typeof import('~icons/ep/circle-check-filled')['default']
    IEpClose: typeof import('~icons/ep/close')['default']
    IEpConnection: typeof import('~icons/ep/connection')['default']
    IEpCopyDocument: typeof import('~icons/ep/copy-document')['default']
    IEpDArrowLeft: typeof import('~icons/ep/d-arrow-left')['default']
    IEpDArrowRight: typeof import('~icons/ep/d-arrow-right')['default']
    IEpDelete: typeof import('~icons/ep/delete')['default']
    IEpDocument: typeof import('~icons/ep/document')['default']
    IEpDocumentCopy: typeof import('~icons/ep/document-copy')['default']
    IEpExpand: typeof import('~icons/ep/expand')['default']
    IEpFold: typeof import('~icons/ep/fold')['default']
    IEpFolder: typeof import('~icons/ep/folder')['default']
    IEpLink: typeof import('~icons/ep/link')['default']
    IEpMoreFilled: typeof import('~icons/ep/more-filled')['default']
    IEpOperation: typeof import('~icons/ep/operation')['default']
    IEpRefresh: typeof import('~icons/ep/refresh')['default']
    IEpRemove: typeof import('~icons/ep/remove')['default']
    IEpSelect: typeof import('~icons/ep/select')['default']
    IEpSetUp: typeof import('~icons/ep/set-up')['default']
    IEpSwitch: typeof import('~icons/ep/switch')['default']
    IEpTools: typeof import('~icons/ep/tools')['default']
    IEpUpload: typeof import('~icons/ep/upload')['default']
    IEpUploadFilled: typeof import('~icons/ep/upload-filled')['default']
    IEpUser: typeof import('~icons/ep/user')['default']
    IEpUserFilled: typeof import('~icons/ep/user-filled')['default']
    ImageCard: typeof import('./components/image-card/image-card.vue')['default']
    ImageHostingDeploy: typeof import('./components/deploy-bar/image-hosting-deploy.vue')['default']
    ImageLinkRuleConfig: typeof import('./components/image-link-rule-config/image-link-rule-config.vue')['default']
    ImageSelector: typeof import('@/views/imgs-management/components/image-selector/image-selector.vue')['default']
    ImgProcessStateCard: typeof import('./components/tools/img-process-state-card/img-process-state-card.vue')['default']
    NavContent: typeof import('./components/nav-content/nav-content.vue')['default']
    QuickActions: typeof import('./components/quick-actions/quick-actions.vue')['default']
    RefreshConfig: typeof import('./components/refresh-config/refresh-config.vue')['default']
    RepoDirCascader: typeof import('./components/repo-dir-cascader/repo-dir-cascader.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectedInfoBar: typeof import('./components/selected-info-bar/selected-info-bar.vue')['default']
    SiteAnnouncement: typeof import('./components/site-announcement/site-announcement.vue')['default']
    SiteCount: typeof import('./components/site-count/site-count.vue')['default']
    UserAvatar: typeof import('./components/user-avatar/user-avatar.vue')['default']
    UserAvatarV2: typeof import('./components/user-avatar-v2/user-avatar-v2.vue')['default']
    WatermarkConfigBox: typeof import('./components/watermark-config-box/watermark-config-box.vue')['default']
    WatermarkTool: typeof import('./components/tools/watermark-tool/watermark-tool.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}