{"repo": "仓库", "branch": "分支", "dir": "目录", "tip": "提示", "delete": "删除", "reset": "重置", "confirm": "确定", "cancel": "取消", "author": "作者", "document": "文档", "shortcut_key": "快捷键", "username": "用户名", "email": "邮箱", "login": "登录", "logout": "退出登录", "language": "语言", "uploaded": "已上传", "upload": "上传", "rename": "重命名", "copy_link": "复制图片链接", "paste_image": "粘贴图片", "copy_success_1": "图片链接已自动复制到系统剪贴板", "copy_success_2": "图片链接复制成功", "copy_fail_1": "复制失败", "header": {"not_login": "未登录", "theme": "主题", "announcement": {"text_1": "网站公告", "text_2": "1、PicX v3.0 进一步简化用户操作，统一使用内置的仓库和分支", "text_3": "2、如果你需要继续使用自定义的仓库和分支，请使用", "text_4": "不再提示"}}, "nav": {"config": "图床配置", "upload": "上传图片", "management": "图床管理", "settings": "图床设置", "toolbox": "工具箱", "feedback": "帮助反馈", "actions": "快捷操作"}, "actions": {"watermark": "图片水印", "compress": "压缩图片", "transform": "转换 "}, "config_page": {"input_token": "请输入 GitHub Token ...", "one_click_config": "一键配置", "loading_1": "正在加载用户信息 ...", "loading_4": "正在加载仓库目录数据 ...", "loading_5": "正在加载目录信息 ...", "loading_6": "正在自动配置 ...", "placeholder_4": "请输入新建的目录 ...", "placeholder_5": "请选择一个目录 ...", "message_1": "GitHub Token 不能为空", "message_2": "用户信息获取失败，请确认 Token 是否正确", "message_3": "自动创建 GitHub 仓库失败，请稍后再试", "message_4": "图床配置成功", "message_5": "一键自动配置失败，请重试", "message_6": "目录不能为空", "message_7": "请输入一个新目录", "message_8": "请选择仓库里的一个目录", "message_9": "仓库信息获取失败，请稍后重试", "dir_mode": "目录模式", "create_new_dir": "新建目录", "input_new_dir": "手动输入一个新目录", "root_dir": "根目录", "date_dir": "日期目录", "date_dir_tip": "自动创建 yyyyMMdd 格式的日期目录", "repo_dir": "仓库目录", "select_dir": "选择目录", "reload": "重新加载"}, "settings_page": {"img_name": {"title": "图片名称设置", "hash_switch_name": "哈希化", "hash_switch_desc": "上传时给图片名称增加哈希值，确保图片名称唯一，强烈建议开启", "prefix_switch_name": "添加前缀", "prefix_switch_desc": "上传时给图片名称增加前缀，例如：abc-image.jpg，abc- 为前缀", "prefix_input_placeholder": "请输入前缀 ..."}, "img_watermark": {"title": "图片水印设置", "switch_name": "是否添加水印", "switch_desc": "开启后可以自定义水印文字、字体大小、位置、颜色和透明度", "text": "水印文字", "text_input_placeholder": "请输入水印文字，限制 20 字", "size": "水印大小", "color": "水印颜色", "opacity": "水印透明度", "position": "水印位置", "position_1": "左上角", "position_2": "左下角", "position_3": "右上角", "position_4": "右下角"}, "img_compress": {"title": "图片压缩设置", "switch_name": "是否压缩图片", "switch_desc": "开启后上传前会自动压缩图片，有效缩短图片上传和加载时间，强烈建议开启", "radio_group_title": "选择图片压缩算法", "radio_1": "webP", "radio_1_desc": "压缩后图片格式为 webp，压缩率较高，大多数浏览器支持", "radio_2": "mozJPEG", "radio_2_desc": "压缩后图片格式为 jpg，兼容性最好，所有浏览器支持", "radio_3": "avif", "radio_3_desc": "压缩后图片格式为 avif，压缩率极高，部分现代浏览器支持"}, "link_rule": {"title": "图片链接规则配置", "select_title": "选择图片链接规则", "card_title": "配置自定义图片链接规则", "card_table_col_title_1": "类型", "card_table_col_title_2": "图片链接规则", "card_table_col_title_3": "操作", "input_name_1": "图片链接类型", "input_name_1_rule": "图片链接类型名称不能为空", "input_name_2": "图片链接规则", "input_name_2_rule": "图片链接规则不能为空", "btn_name_1": "添加图片链接规则", "error_msg_1": "添加失败，该图片链接规则已存在", "error_msg_2": "{action}失败，图片链接规则必须包含 {path}", "error_msg_3": "图片链接规则缺少 {rules}，是否确认{action}？", "add": "添加", "edit": "编辑"}, "link_format": {"title": "图片链接格式设置", "switch_name": "自动转换图片链接格式", "switch_desc": "上传成功后复制的图片链接自动转换成 {type} 格式", "select_title": "选择图片链接格式", "delete_tips": "此操作将永久删除图片链接规则"}, "image_hosting_deploy": {"title": "图床部署", "one_click_deploy": "一键部署", "deploy_to": "部署到 {server}", "success": "部署成功", "fail": "部署失败", "fail2": "部署失败，请稍后再试", "deploying": "正在部署到 GitHub Pages，该过程持续 1 分钟左右，请耐心等待 ...", "not_deployed": "未部署"}, "theme": {"title": "主题设置", "system": "跟随系统", "dark": "暗夜", "light": "白昼"}, "cloud_settings": {"tip_1": "检测到你的设置数据未保存到云端仓库，是否保存？", "tip_2": "检测到你的云端仓库存在设置文件，是否使用？", "tip_3": "检测到你的设置数据有变化，是否同步到云端仓库？", "tip_4": "本地设置数据和云端仓库设置数据一致", "success_msg_1": "保存成功", "success_msg_2": "更新成功"}}, "upload_page": {"upload_area_text": "拖拽 / 粘贴 / 点击此处选择图片", "message1": "请先完成图床配置", "message2": "请选择一个仓库", "message3": "目录不能为空", "message4": "请选择要上传的图片", "message5": "图片上传成功", "message6": "图片批量上传成功", "message7": "上传失败，请稍后重试", "message8": "图片上传成功", "fold": "折叠", "expand": "展开", "add_hash": "哈希化", "add_prefix": "添加前缀", "uploading": "正在上传 ...", "tip_9": "{name} 不是图片格式", "tip_10": "{name} 超过 {size} MB，跳过选择", "tip_11": "{name} 上传失败"}, "management_page": {"reload": "重新加载", "loadingTxt1": "加载中 ...", "loadingTxt2": "正在重命名 ...", "loadingTxt3": "删除中 ...", "property": "属性", "delTips": "此操作将会永久删除图片", "delTips2": "已选中 {total} 张图片，是否批量删除？", "message1": "图片名称不能为空", "message2": "图片名无改变", "message3": "重命名失败，请重试", "message4": "更新成功", "message5": "删除成功", "message6": "批量删除成功", "message7": "删除失败，请稍后重试", "imageName": "图片名称", "imageSize": "图片大小", "selectAll": "全选", "deselectAll": "取消全选", "unselect": "取消选择", "batchCopy": "批量复制图片链接", "batchDelete": "批量删除图片", "selectTotal": "已选择 {total} 张图片", "contextmenu_1": "从当前位置上传新图片", "contextmenu_2": "上传图片到 {dir}", "contextmenu_3": "根目录", "text_1": "请输入新的图片名称", "text_2": "立即上传"}, "toolbox": {"tool_1": "图片压缩", "tool_1_desc": "不限制图片大小和数量，不上传至服务器的离线极致压缩", "tool_2": "图片转 Base64", "tool_2_desc": "不限制图片大小和数量，在线转换成 Base64 编码", "tool_3": "图片水印 ", "tool_3_desc": "自定义水印文字、字体大小、位置、颜色和透明度", "copy_base64": "点击复制 Base64 编码", "copy_base64_success": "Base64 编码复制成功", "click_download": "点击下载", "compress": "压缩", "batch_download": "批量下载", "add_watermark": "添加水印"}, "feedback": {"text_1": "PicX 是一款基于 GitHub API 开发的图床工具，提供图片上传托管、生成图片链接和常用图片工具箱服务。", "text_2": "建议将本站添加到浏览器收藏夹，方便下次使用。", "text_3": "如果 PicX 对你有帮助，欢迎赞赏作者，支持开源。", "text_4": "郑重声明：请勿使用 PicX 上传违反你当地法律的图片，所造成的一切后果与作者无关。"}, "region": {"CN": "中国大陆", "HK": "中国香港", "MO": "中国澳门", "TW": "中国台湾", "SG": "新加坡", "JP": "日本", "US": "美国", "NL": "荷兰"}, "languages": {"zh-CN": "中文简体", "zh-TW": "中文繁体", "en": "英文"}, "toggle_language_msg": "检测到你的 IP 所属地为{region}，是否切换{language}？", "authorization": {"msg_1": "GitHub OAuth 授权登录已过期，请重新授权", "msg_2": "未在 GitHub 安装 PicX APP，暂无操作权限", "msg_3": "PicX GitHub APP 安装成功，是否进行授权登录？", "msg_4": "授权失败，稍后重试", "btn_1": "立即安装", "loading_1": "正在授权登录...", "text_1": "GitHub OAuth 授权登录", "text_2": "填写 GitHub Token 登录", "text_3": "正在使用 GitHub OAuth 授权登录", "text_4": "GitHub Token 已过期，请重新授权登录", "text_5": "正在使用填写 GitHub Token 登录", "text_6": "返回登录页", "text_7": "切换", "text_8": "使用 GitHub OAuth 授权登录必须安装 PicX GitHub APP", "text_9": "填写 GitHub Token 登录必须生成具有操作权限的 Token", "text_10": "查看教程", "text_11": "安装 PicX GitHub APP", "text_12": "创建 GitHub Token", "text_13": "立即授权"}, "copy_repo_img": {"text_1": "复制仓库图片", "text_2": "请输入仓库名称", "text_3": "请输入分支名称", "text_4": "仓库或分支不正确，请重新输入", "text_6": "{repo} 仓库图片复制成功", "text_7": "{repo} 仓库图片复制失败，稍后重试", "text_8": "复制其他仓库图片", "loading_1": "复制中"}}