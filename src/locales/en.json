{"repo": "Repository", "branch": "Branch", "dir": "Directory", "tip": "Tip", "delete": "Delete", "reset": "Reset", "confirm": "Confirm", "cancel": "Cancel", "author": "Author", "document": "Document", "shortcut_key": "Shortcut Key", "username": "Username", "email": "Email", "login": "Log In", "logout": "Log Out", "language": "Language", "uploaded": "Uploaded", "upload": "Upload", "rename": "<PERSON><PERSON>", "copy_link": "Copy Image Link", "paste_image": "Paste images", "copy_success_1": "Image link has been automatically copied to the system clipboard", "copy_success_2": "Image link copied successfully", "copy_fail_1": "Co<PERSON> failed", "header": {"not_login": "Not log in", "theme": "Theme", "announcement": {"text_1": "Website Announcement", "text_2": "1. PicX v3.0 further simplifies user operations and uniformly uses built-in repository and branch", "text_3": "2. If you need to continue to use customized repository and branch, please use", "text_4": "Don't remind again"}}, "nav": {"config": "Image Hosting Configuration", "upload": "Upload Image", "management": "Image Hosting Management", "settings": "Image Hosting Settings", "toolbox": "Toolbox", "feedback": "<PERSON><PERSON><PERSON>", "actions": "Quick Actions"}, "actions": {"watermark": "Image watermark", "compress": "Compress image", "transform": "Transform "}, "config_page": {"input_token": "Please enter GitHub Token...", "one_click_config": "One-Click Configuration", "loading_1": "Loading user information...", "loading_4": "Loading repository directory data...", "loading_5": "Loading directory information...", "loading_6": "Auto-configuring...", "placeholder_4": "Please enter the name of the new directory...", "placeholder_5": "Please select a directory...", "message_1": "GitHub Token cannot be empty", "message_2": "Failed to retrieve user information. Please check if the Token is correct", "message_3": "Auto-creation of GitHub repository failed. Please try again later", "message_4": "Image hosting configuration successful", "message_5": "One-click auto-configuration failed. Please retry", "message_6": "Directory cannot be empty", "message_7": "Please enter a new directory", "message_8": "Please select a directory in the repository", "message_9": "Failed to retrieve repository information. Please try again later", "dir_mode": "Directory Mode", "create_new_dir": "Create New Directory", "input_new_dir": "Manually input a new directory", "root_dir": "Root Directory", "date_dir": "Date Directory", "date_dir_tip": "Automatically create directories in the format yyyyMMdd", "repo_dir": "Repository Directory", "select_dir": "Select Directory", "reload": "Reload"}, "settings_page": {"img_name": {"title": "Image Name Settings", "hash_switch_name": "Hashing", "hash_switch_desc": "Add a hash value to the image name during upload to ensure uniqueness. Highly recommended to enable.", "prefix_switch_name": "Add Prefix", "prefix_switch_desc": "Add a prefix to the image name during upload, for example: abc-image.jpg, where 'abc-' is the prefix.", "prefix_input_placeholder": "Enter prefix ..."}, "img_watermark": {"title": "Image Watermark Settings", "switch_name": "Add Watermark", "switch_desc": "Customize watermark text, font size, position, color, and opacity when enabled.", "text": "Text", "text_input_placeholder": "Enter watermark text, limit to 20 characters", "size": "Size", "color": "Color", "opacity": "Opacity", "position": "Position", "position_1": "Top Left", "position_2": "Bottom Left", "position_3": "Top Right", "position_4": "Bottom Right"}, "img_compress": {"title": "Image Compression Settings", "switch_name": "Compress Images", "switch_desc": "Automatically compress images before upload, reducing upload and loading times. Highly recommended to enable.", "radio_group_title": "Choose Image Compression Algorithm", "radio_1": "WebP", "radio_1_desc": "Resulting format is webp, offering high compression rates, supported by most browsers", "radio_2": "MozJPEG", "radio_2_desc": "Resulting format is jpg, highest compatibility with all browsers", "radio_3": "AVIF", "radio_3_desc": "Resulting format is avif, extremely high compression rates, supported by some modern browsers"}, "link_rule": {"title": "Image Link Rules Configuration", "select_title": "Select Image Link Rule", "card_title": "Configure Custom Image Link Rules", "card_table_col_title_1": "Type", "card_table_col_title_2": "Image Link Rule", "card_table_col_title_3": "Action", "input_name_1": "Image Link Type", "input_name_1_rule": "Image link type name cannot be empty", "input_name_2": "Image Link Rule", "input_name_2_rule": "Image link rule cannot be empty", "btn_name_1": "Add Image Link Rule", "error_msg_1": "Failed to add. This image link rule already exists", "error_msg_2": "{action} failed. Image link rule must include {path}", "error_msg_3": "Image link rule is missing {rules}. Confirm {action}?", "add": "Add", "edit": "Edit"}, "link_format": {"title": "Image Link Format Settings", "switch_name": "Automatically Convert Image Link Format", "switch_desc": "Automatically converts copied image links to {type} format after successful upload", "select_title": "Select Image Link Format", "delete_tips": "This action will permanently delete the image link rule"}, "image_hosting_deploy": {"title": "Image Hosting Deployment", "one_click_deploy": "One-<PERSON><PERSON> De<PERSON>loy", "deploy_to": "Deploy to {server}", "success": "Deployment successful", "fail": "Deployment failed", "fail2": "Deployment failed, please try again later", "deploying": "Deploying to GitHub Pages, this process takes about 1 minute, please be patient...", "not_deployed": "Not deployed"}, "theme": {"title": "Theme Settings", "system": "System", "dark": "Dark", "light": "Light"}, "cloud_settings": {"tip_1": "Detected that your settings data is not saved to the cloud repository. Save now?", "tip_2": "Detected that your cloud repository has settings files. Use them?", "tip_3": "Detected changes in your settings data. Synchronize with the cloud repository?", "tip_4": "Local and cloud repository settings data are consistent", "success_msg_1": "Save successful", "success_msg_2": "Update successful"}}, "upload_page": {"upload_area_text": "Drag / Paste / Click here to select images", "message1": "Please complete image hosting configuration first", "message2": "Please select a repository", "message3": "Directory cannot be empty", "message4": "Please select images to upload", "message5": "Image uploaded successfully", "message6": "Bulk image upload successful", "message7": "Upload failed, please try again later", "message8": "Image uploaded successfully", "fold": "Fold", "expand": "Expand", "add_hash": "<PERSON><PERSON>", "add_prefix": "Add Prefix", "uploading": "Uploading...", "tip_9": "{name} is not an image format", "tip_10": "{name} exceeds {size} MB, skip selection", "tip_11": "{name} upload failed"}, "management_page": {"reload": "Reload", "loadingTxt1": "Loading...", "loadingTxt2": "Renaming...", "loadingTxt3": "Deleting...", "property": "Properties", "delTips": "This action will permanently delete the image", "delTips2": "Selected {total} images, do you want to delete them in bulk?", "message1": "Image name cannot be empty", "message2": "No changes in image name", "message3": "<PERSON><PERSON> failed, please try again", "message4": "Update successful", "message5": "Deletion successful", "message6": "Bulk deletion successful", "message7": "Deletion failed, please try again later", "imageName": "Image Name", "imageSize": "Image Size", "selectAll": "Select All", "deselectAll": "Deselect All", "unselect": "Deselect", "batchCopy": "Batch Copy Image Links", "batchDelete": "Batch Delete Images", "selectTotal": "Selected {total} images", "contextmenu_1": "Upload new images from the current location", "contextmenu_2": "Upload images to {dir}", "contextmenu_3": "Root Directory", "text_1": "Please enter a new image name", "text_2": "Upload now"}, "toolbox": {"tool_1": "Image Compression", "tool_1_desc": "Ultra-compression offline without limitations on image size and quantity, without uploading to the server", "tool_2": "Image to Base64", "tool_2_desc": "Convert images to Base64 encoding online without limitations on size and quantity", "tool_3": "Image Watermark", "tool_3_desc": "Customize watermark text, font size, position, color, and opacity", "copy_base64": "Click to copy Base64 encoding", "copy_base64_success": "Base64 encoding copied successfully", "click_download": "Click to download", "compress": "Compress", "batch_download": "Batch Download", "add_watermark": "Add Watermark"}, "feedback": {"text_1": "PicX is an image hosting tool developed based on the GitHub API, providing services for image upload hosting, generating image links, and common image tools.", "text_2": "It is recommended to add this site to your browser bookmarks for convenient use in the future.", "text_3": "If PicX is helpful to you, feel free to appreciate the author and support open source.", "text_4": "Disclaimer: Do not use PicX to upload images that violate local laws. The author is not responsible for any consequences."}, "region": {"CN": "Mainland China", "HK": "Hong Kong, China", "MO": "Macau, China", "TW": "Taiwan, China", "SG": "Singapore", "JP": "Japan", "US": "United States", "NL": "Netherlands"}, "languages": {"zh-CN": "Simplified Chinese", "zh-TW": "Traditional Chinese", "en": "English"}, "toggle_language_msg": "Detected that your IP belongs to {region}. Do you want to switch to {language}?", "authorization": {"msg_1": "GitHub OAuth authorization has expired. Please reauthorize.", "msg_2": "PicX APP is not installed on GitHub, no operational permissions currently.", "msg_3": "PicX GitHub APP installed successfully. Would you like to authorize and log in?", "msg_4": "Authorization failed. Please try again later.", "btn_1": "Install Now", "loading_1": "Authorizing login...", "text_1": "GitHub OAuth Authorization Login", "text_2": "Login with GitHub Token", "text_3": "Logging in using GitHub OAuth Authorization", "text_4": "GitHub Token has expired. Please reauthorize login.", "text_5": "Logging in using GitHub Token", "text_6": "Return to Login Page", "text_7": "Switch", "text_8": "GitHub OAuth Authorization Login requires PicX GitHub APP installation.", "text_9": "Login with GitHub Token requires generating a Token with operational permissions.", "text_10": "View Tutorial", "text_11": "Install PicX GitHub APP", "text_12": "Create GitHub Token", "text_13": "Authorize now"}, "copy_repo_img": {"text_1": "Copy repository images", "text_2": "Please enter the repository name", "text_3": "Please enter the branch name", "text_4": "The repository or branch is incorrect, please re-enter", "text_6": "{repo} repository images copied successfully", "text_7": "{repo} repository images copy failed, try again later", "text_8": "Copy other repository images", "loading_1": "<PERSON><PERSON><PERSON>"}}