{"repo": "倉庫", "branch": "分支", "dir": "目錄", "tip": "提示", "delete": "刪除", "reset": "重置", "confirm": "確定", "cancel": "取消", "author": "作者", "document": "文件", "shortcut_key": "快速鍵", "username": "名稱", "email": "信箱", "login": "登入", "logout": "登出", "language": "語言", "uploaded": "已上傳", "upload": "上傳", "rename": "重新命名", "copy_link": "複製圖片連結", "paste_image": "貼上圖片", "copy_success_1": "圖片連結已自動複製到系統剪貼簿", "copy_success_2": "圖片連結複製成功", "copy_fail_1": "複製失敗", "header": {"not_login": "未登入", "theme": "主題", "announcement": {"text_1": "網站公告", "text_2": "1、PicX v3.0 進一步簡化使用者操作，統一使用內建的倉庫和分支", "text_3": "2、如果你需要繼續使用自訂的倉庫和分支，請使用", "text_4": "不再提示"}}, "nav": {"config": "圖床配置", "upload": "上傳圖片", "management": "圖床管理", "settings": "圖床設定", "toolbox": "工具箱", "feedback": "幫助回饋", "actions": "快速操作"}, "actions": {"watermark": "圖片浮水印", "compress": "壓縮圖片", "transform": "轉換 "}, "config_page": {"input_token": "請輸入 GitHub Token ...", "one_click_config": "一鍵配置", "loading_1": "正在載入使用者資訊 ...", "loading_4": "正在載入倉庫目錄資料 ...", "loading_5": "正在載入目錄資訊 ...", "loading_6": "正在自動設定 ...", "placeholder_4": "請輸入新建的目錄 ...", "placeholder_5": "請選擇目錄 ...", "message_1": "GitHub Token 不能為空", "message_2": "使用者資訊取得失敗，請確認 Token 是否正確", "message_3": "自動建立 GitHub 倉庫失敗，請稍後再試", "message_4": "圖床配置成功", "message_5": "一鍵自動設定失敗，請重試", "message_6": "目錄不能為空", "message_7": "請輸入一個新目錄", "message_8": "請選擇倉庫裡的目錄", "message_9": "倉庫資訊取得失敗，請稍後重試", "dir_mode": "目錄模式", "create_new_dir": "新目錄", "input_new_dir": "手動輸入一個新目錄", "root_dir": "根目錄", "date_dir": "日期目錄", "date_dir_tip": "自動建立 yyyyMMdd 格式的日期目錄", "repo_dir": "倉庫目錄", "select_dir": "選擇目錄", "reload": "重新載入"}, "settings_page": {"img_name": {"title": "圖片名稱設定", "hash_switch_name": "哈希化", "hash_switch_desc": "上傳時為圖片名稱增加雜湊值，確保圖片名稱唯一，強烈建議開啟", "prefix_switch_name": "加上前綴", "prefix_switch_desc": "上傳時為圖片名稱增加前綴，例如：abc-image.jpg，abc- 為前綴", "prefix_input_placeholder": "請輸入字首 ..."}, "img_watermark": {"title": "圖片浮水印設定", "switch_name": "是否新增浮水印", "switch_desc": "開啟後可以自訂浮水印文字、字體大小、位置、顏色和透明度", "text": "水印文字", "text_input_placeholder": "請輸入浮水印文字，限制 20 字", "size": "浮水印大小", "color": "水印顏色", "opacity": "浮水印透明度", "position": "浮水印位置", "position_1": "左上角", "position_2": "左下角", "position_3": "右上角", "position_4": "右下角"}, "img_compress": {"title": "圖片壓縮設定", "switch_name": "是否壓縮圖片", "switch_desc": "開啟後上傳前會自動壓縮圖片，有效縮短圖片上傳與載入時間，強烈建議開啟", "radio_group_title": "選擇圖片壓縮演算法", "radio_1": "webP", "radio_1_desc": "壓縮後圖片格式為 webp，壓縮率較高，大多數瀏覽器支援", "radio_2": "mozJPEG", "radio_2_desc": "壓縮後圖片格式為 jpg，相容性最好，所有瀏覽器支援", "radio_3": "avif", "radio_3_desc": "壓縮後圖片格式為 avif，壓縮率極高，部分現代瀏覽器支援"}, "link_rule": {"title": "圖片連結規則配置", "select_title": "選擇圖片連結規則", "card_title": "配置自訂圖片連結規則", "card_table_col_title_1": "型別", "card_table_col_title_2": "圖片連結規則", "card_table_col_title_3": "操作", "input_name_1": "圖片連結類型", "input_name_1_rule": "圖片連結類型名稱不能為空", "input_name_2": "圖片連結規則", "input_name_2_rule": "圖片連結規則不能為空", "btn_name_1": "新增圖片連結規則", "error_msg_1": "新增失敗，此圖片連結規則已存在", "error_msg_2": "{action}失敗，圖片連結規則必須包含 {path}", "error_msg_3": "圖片連結規則缺少 {rules}，是否確認{action}？", "add": "新增", "edit": "編輯"}, "link_format": {"title": "圖片連結格式設定", "switch_name": "自動轉換圖片連結格式", "switch_desc": "上傳成功後複製的圖片連結會自動轉換成 {type} 格式", "select_title": "選擇圖片連結格式", "delete_tips": "此動作將永久刪除圖片連結規則"}, "image_hosting_deploy": {"title": "圖床部署", "one_click_deploy": "一鍵部署", "deploy_to": "部署到 {server}", "success": "部署成功", "fail": "部署失敗", "fail2": "部署失敗，請稍後再試", "deploying": "正在部署到 GitHub Pages，該過程持續 1 分鐘左右，請耐心等待 ...", "not_deployed": "未部署"}, "theme": {"title": "主題設定", "system": "跟隨系統", "dark": "黑夜", "light": "白晝"}, "cloud_settings": {"tip_1": "偵測到你的設定資料未儲存到雲端倉庫，是否儲存？", "tip_2": "偵測到你的雲端倉庫存在設定文件，是否使用？", "tip_3": "偵測到你的設定資料有變化，是否同步到雲端倉庫？", "tip_4": "本地設定資料與雲端倉庫設定資料一致", "success_msg_1": "保存成功", "success_msg_2": "更新成功"}}, "upload_page": {"upload_area_text": "拖曳 / 貼上 / 點擊此處選擇圖片", "message1": "請先完成圖床配置", "message2": "請選擇一個倉庫", "message3": "目錄不能為空", "message4": "請選擇要上傳的圖片", "message5": "圖片上傳成功", "message6": "圖片批次上傳成功", "message7": "上傳失敗，請稍後重試", "message8": "圖片上傳成功", "fold": "折疊", "expand": "展開", "add_hash": "哈希化", "add_prefix": "新增前綴", "uploading": "正在上傳 ...", "tip_9": "{name} 不是圖片格式", "tip_10": "{name} 超過 {size} MB，跳過選擇", "tip_11": "{name} 上傳失敗"}, "management_page": {"reload": "重新載入", "loadingTxt1": "載入中 ...", "loadingTxt2": "正在重新命名 ...", "loadingTxt3": "刪除中 ...", "property": "屬性", "delTips": "此操作將會永久刪除圖片", "delTips2": "已選取 {total} 張圖片，是否已批次刪除？", "message1": "圖片名稱不能為空", "message2": "圖片名稱無改變", "message3": "重新命名失敗，請重試", "message4": "更新成功", "message5": "刪除成功", "message6": "批次刪除成功", "message7": "刪除失敗，請稍後重試", "imageName": "圖片名稱", "imageSize": "圖片大小", "selectAll": "全選", "deselectAll": "取消全選", "unselect": "取消選擇", "batchCopy": "批次複製圖片連結", "batchDelete": "批次刪除圖片", "selectTotal": "已選擇 {total} 張圖片", "contextmenu_1": "從目前位置上傳新圖片", "contextmenu_2": "上傳圖片到 {dir}", "contextmenu_3": "根目錄", "text_1": "請輸入新的圖片名稱", "text_2": "立即上傳"}, "toolbox": {"tool_1": "圖片壓縮", "tool_1_desc": "不限制圖片大小和數量，不上傳至伺服器的離線極致壓縮", "tool_2": "圖片轉 Base64", "tool_2_desc": "不限制圖片大小和數量，線上轉換成 Base64 編碼", "tool_3": "圖片浮水印 ", "tool_3_desc": "自訂浮水印文字、字體大小、位置、顏色和透明度", "copy_base64": "點選複製 Base64 編碼", "copy_base64_success": "Base64 編碼複製成功", "click_download": "點選下載", "compress": "壓縮", "batch_download": "批次下載", "add_watermark": "新增浮水印"}, "feedback": {"text_1": "PicX 是一款基於 GitHub API 開發的圖床工具，提供圖片上傳託管、產生圖片連結和常用圖片工具箱服務。", "text_2": "建議將本站新增至瀏覽器收藏夾，方便下次使用。", "text_3": "如果 PicX 對你有幫助，歡迎讚賞作者，支持開源。", "text_4": "鄭重聲明：請勿使用 PicX 上傳違反你當地法律的圖片，所造成的一切後果與作者無關。"}, "region": {"CN": "中國大陸", "HK": "中國香港", "MO": "中國澳門", "TW": "中國台灣", "SG": "新加坡", "JP": "日本", "US": "美國", "NL": "荷蘭"}, "languages": {"zh-CN": "中文簡體", "zh-TW": "中文繁體", "en": "英文"}, "toggle_language_msg": "偵測到你的 IP 所屬地為{region}，是否切換{language}？", "authorization": {"msg_1": "GitHub OAuth 授權登入已過期，請重新授權", "msg_2": "未在 GitHub 安裝 PicX APP，暫無操作權限", "msg_3": "PicX GitHub APP 安裝成功，是否進行授權登入？", "msg_4": "授權失敗，稍後重試", "btn_1": "立即安裝", "loading_1": "正在授權登入...", "text_1": "GitHub OAuth 授權登入", "text_2": "填入 GitHub Token 登入", "text_3": "正在使用 GitHub OAuth 授權登入", "text_4": "GitHub Token 已過期，請重新授權登入", "text_5": "正在使用填入 GitHub Token 登入", "text_6": "返回登入頁面", "text_7": "切換", "text_8": "使用 GitHub OAuth 授權登入必須安裝 PicX GitHub APP", "text_9": "填寫 GitHub Token 登入必須產生具有操作權限的 Token", "text_10": "檢視教學", "text_11": "安裝 PicX GitHub APP", "text_12": "建立 GitHub Token", "text_13": "立即授權"}, "copy_repo_img": {"text_1": "複製倉庫圖片", "text_2": "請輸入倉庫名稱", "text_3": "請輸入分支名稱", "text_4": "倉庫或分支不正確，請重新輸入", "text_6": "{repo} 倉庫圖片複製成功", "text_7": "{repo} 倉庫圖片複製失敗，稍後重試", "text_8": "複製其他倉庫圖片", "loading_1": "複製中"}}