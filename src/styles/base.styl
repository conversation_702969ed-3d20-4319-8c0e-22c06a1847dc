@import "./variables.styl"
@import "./theme.styl"
@import "./element-plus.styl"

:root {
  font-size 1px

  +picx-tablet() {
    font-size 0.95px
  }


  +picx-tablet() {
    font-size 0.9px
  }


  +picx-mobile() {
    font-size 0.8px
  }
}


* {
  &::-webkit-scrollbar {
    width $scrollbar-size
    height $scrollbar-size
  }

  &::-webkit-scrollbar-thumb {
    background var(--scrollbar-color)
    border-radius $box-border-radius
  }

  &::-webkit-scrollbar-track {
    background transparent
  }
}


html
body {
  position relative
  width 100%
  height 100%
  margin 0
  padding 0
  color var(--text-color)
  font-size 14rem
}


a {
  color var(--text-color)
  text-decoration none

  &:link {
    color var(--text-color)
    text-decoration none
  }
}


ul
ol
li {
  margin 0
  padding 0
  list-style none
}


.flex-center {
  display flex
  align-items center
  justify-content center
}


.flex-start {
  display flex
  align-items center
  justify-content flex-start
}

.flex-end {
  display flex
  align-items center
  justify-content flex-end
}


.border-box {
  position relative
  box-sizing border-box
}


.text-ellipsis {
  overflow hidden
  white-space nowrap
  text-overflow ellipsis
}


.clearfix {
  &::after {
    display block
    clear both
    height 0
    overflow hidden
    visibility hidden
    content ''
  }
}


.page-container {
  position relative
  box-sizing border-box
  width 100%
  height 100%
  padding 30rem
  overflow-x auto
  overflow-y auto
  background var(--background-color)
  border-top-left-radius $box-border-radius
  border-top-right-radius $box-border-radius
}


.custom-contextmenu-container {
  position relative
  box-sizing border-box
  padding 5rem 0
  color var(--text-color)
  background var(--background-color)
  border-radius 3rem
  box-shadow var(--el-box-shadow-light)

  .custom-contextmenu-item {
    position relative
    display none
    align-items center
    justify-content flex-start
    box-sizing border-box
    min-width 120rem
    padding 8rem 10rem
    font-size 12rem
    cursor pointer

    &:hover {
      color var(--el-color-primary)
      background var(--el-color-primary-light-9)
    }
  }
}


.quick-actions-popover {
  position relative
  box-sizing border-box
  width 100%
  padding 10rem 12rem
  background var(--background-color)
  user-select none

  .el-switch {
    white-space nowrap
  }
}


.personal-center-popover {
  width 100%
  padding 10rem 0
  background var(--background-color)

  .user-info {
    width 100%
    padding 6rem 12rem

    .info-item {
      margin-bottom 2rem
    }

    .owner {
      color var(--text-color-3)
      font-size 16rem
    }

    .name {
      color var(--text-color-4)
      font-size 13rem
    }
  }


  .content-item {
    display flex
    align-items center
    justify-content space-between
    width 100%
    padding 8rem 12rem
    color var(--text-color-3)
    font-size 14rem
    cursor pointer

    &:hover {
      color var(--el-color-primary)
      background var(--el-color-primary-light-9)
    }
  }

  .el-divider {
    margin 0
  }
}


.global-settings-popover {
  width 100%
  padding 10rem 0
  background var(--background-color)

  .content-item {
    display flex
    align-items center
    justify-content space-between
    width 100%
    height 36rem
    padding 0 12rem
    color var(--text-color-3)
    font-size 14rem
    cursor pointer

    &:hover {
      background var(--background-color-2)
    }
  }
}


.custom-message-container {
  box-sizing border-box

  .content-box {
    line-height 2

    .btn-box {
      margin-left 6rem
    }

    .btn {
      display inline-block
      margin-left 2rem
      padding 0 6rem
      font-size 12rem
      border-style solid
      border-width 1rem
      border-radius 4rem
      cursor pointer
    }


    .confirm {
      color var(--el-color-primary)
      background var(--el-color-primary-light-9)
      border-color var(--el-color-primary-light-5)

      &:hover {
        color var(--el-color-white)
        background var(--el-color-primary)
        border-color var(--el-color-primary)
      }
    }


    .cancel {
      color var(--el-color-info)
      background var(--el-color-info-light-9)
      border-color var(--el-color-info-light-5)

      &:hover {
        color var(--el-color-white)
        background var(--el-color-info)
        border-color var(--el-color-info)
      }
    }
  }
}

.status-bar {
  position relative
  box-sizing border-box
  padding-left 12rem
  overflow hidden
  color var(--el-color-info)
  font-size 13.2rem
  background-color var(--el-color-info-light-9)
  border-color var(--el-color-info-light-5)
  border-style solid
  border-width 1rem
  border-radius 6rem

  &.info {
    color var(--el-color-info)
    background-color var(--el-color-info-light-9)
    border-color var(--el-color-info-light-5)
  }

  &.success {
    color var(--el-color-success)
    background-color var(--el-color-success-light-9)
    border-color var(--el-color-success-light-5)
  }

  &.warning {
    color var(--el-color-warning)
    background-color var(--el-color-warning-light-9)
    border-color var(--el-color-warning-light-5)
  }

  &.error {
    color var(--el-color-danger)
    background-color var(--el-color-danger-light-9)
    border-color var(--el-color-danger-light-5)
  }
}




