{"name": "picx", "version": "3.0.2", "private": false, "packageManager": "pnpm@7.33.7", "author": "XPoet <<EMAIL>>", "license": "AGPL-3.0", "bugs": {"url": "https://github.com/XPoet/picx/issues"}, "homepage": "https://github.com/XPoet/picx#readme", "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview", "format": "prettier --write", "lint": "eslint --fix ./src --ext .vue,.js,.ts", "lint:style": "stylelint --fix ./src/**/**/*.{vue,styl}", "prepare": "husky install"}, "lint-staged": {"*.{vue,styl}": "stylelint --fix", "*.{vue,js,ts}": "eslint --fix"}, "config": {"commitizen": {"path": "cz-custom"}, "cz-custom": {"config": ".cz-config.js"}}, "dependencies": {"@yireen/squoosh-browser": "^1.0.7", "axios": "^1.3.5", "element-plus": "2.3.7", "vue": "^3.2.47", "vue-i18n": "^9.2.2", "vue-router": "^4.1.6", "vuex": "^4.1.0"}, "devDependencies": {"@commitlint/cli": "17.0.3", "@commitlint/config-conventional": "^17.0.3", "@iconify-json/ep": "^1.1.10", "@plugin-web-update-notification/vite": "1.7.1", "@types/node": "^15.0.1", "@typescript-eslint/eslint-plugin": "^4.22.0", "@typescript-eslint/parser": "^4.22.0", "@vitejs/plugin-vue": "^2.3.4", "@vue/compiler-sfc": "^3.2.47", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "cz-custom": "^0.0.2", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-vue": "^9.1.1", "husky": "^6.0.0", "lint-staged": "^10.5.4", "prettier": "^2.2.1", "stylelint": "^15.3.0", "stylelint-config-rational-order": "^0.1.2", "stylelint-stylus": "^0.18.0", "stylus": "^0.59.0", "terser": "^5.19.2", "typescript": "^4.1.5", "unplugin-auto-import": "^0.15.2", "unplugin-icons": "^0.16.1", "unplugin-vue-components": "^0.24.1", "vite": "~2.7.13", "vite-plugin-pwa": "^0.12.8"}}