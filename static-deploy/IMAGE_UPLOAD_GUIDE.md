# 图片上传功能使用指南

## 功能概述

本项目已成功集成了基于GitHub的图床功能，支持以下特性：

- 🖱️ **拖拽上传**：直接将图片拖拽到编辑器区域即可上传
- 📁 **按钮上传**：点击工具栏的图片按钮选择文件上传
- 📋 **粘贴上传**：支持从剪贴板粘贴图片直接上传
- 🔄 **自动重试**：上传失败时自动重试，提高成功率
- 📊 **进度显示**：实时显示上传进度和状态
- 🔗 **自动插入**：上传成功后自动插入Markdown图片链接

## 配置要求

### 1. GitHub配置
在使用图片上传功能前，需要先配置GitHub：

1. **Personal Access Token**：
   - 访问 GitHub → Settings → Developer settings → Personal access tokens → Tokens (classic)
   - 点击 "Generate new token (classic)"
   - 勾选 `repo` 权限
   - 生成并复制token

2. **仓库信息**：
   - Repository Owner：GitHub用户名或组织名
   - Repository Name：用于存储图片的仓库名

### 2. 仓库结构
图片将按以下结构存储：
```
your-repo/
├── images/
│   ├── 2024/
│   │   ├── 01/
│   │   │   ├── 01/
│   │   │   │   ├── image1_timestamp.jpg
│   │   │   │   └── image2_timestamp.png
│   │   │   └── 02/
│   │   └── 02/
│   └── 2025/
```

## 使用方法

### 方法一：拖拽上传
1. 在文件管理器中选择图片文件
2. 直接拖拽到编辑器区域
3. 看到蓝色覆盖层时松开鼠标
4. 等待上传完成，图片链接会自动插入编辑器

### 方法二：按钮上传
1. 点击工具栏中的图片按钮（📷图标）
2. 在弹出的文件选择对话框中选择图片
3. 支持多选，可一次选择多张图片
4. 等待上传完成

### 方法三：粘贴上传
1. 在其他应用中复制图片（如截图工具、浏览器等）
2. 在编辑器中按 `Ctrl+V`（Windows）或 `Cmd+V`（Mac）
3. 图片会自动上传并插入链接

## 支持的图片格式

- **JPEG/JPG**：最常用的图片格式
- **PNG**：支持透明背景
- **GIF**：支持动画
- **WebP**：现代高效格式
- **SVG**：矢量图形格式

## 文件大小限制

- **最大文件大小**：10MB
- **最小文件大小**：1KB
- **建议大小**：1-5MB，平衡质量和加载速度

## 错误处理

### 常见错误及解决方案

1. **"GitHub未配置"**
   - 解决：点击设置按钮配置GitHub信息

2. **"文件必须是图片格式"**
   - 解决：确保选择的是支持的图片格式

3. **"图片大小不能超过10MB"**
   - 解决：使用图片压缩工具减小文件大小

4. **"GitHub API Error: 401"**
   - 解决：检查Personal Access Token是否正确且有效

5. **"GitHub API Error: 404"**
   - 解决：检查仓库名和用户名是否正确

6. **网络连接错误**
   - 解决：检查网络连接，系统会自动重试

## 高级功能

### 自动重试机制
- 上传失败时自动重试最多2次
- 使用指数退避策略，避免频繁请求
- 重试间隔：2秒、4秒

### 批量上传优化
- 支持同时上传多张图片
- 显示整体进度和单个文件状态
- 失败的文件不影响其他文件上传

### 文件名处理
- 自动清理特殊字符，确保兼容性
- 添加时间戳避免文件名冲突
- 保持原始文件扩展名

## 测试功能

项目包含完整的测试页面 `test-image-upload.html`，可以测试：

1. **服务初始化**：检查服务是否正确加载
2. **文件验证**：测试文件格式和大小验证
3. **拖拽功能**：测试拖拽上传
4. **按钮上传**：测试点击上传
5. **批量上传**：测试多文件上传

### 运行测试
1. 在浏览器中打开 `test-image-upload.html`
2. 配置GitHub信息
3. 按顺序运行各项测试
4. 查看测试结果和日志

## 技术实现

### 核心组件
- **GitHubService**：处理GitHub API交互
- **ImageUploadService**：处理图片上传逻辑
- **Vue.js集成**：与编辑器的无缝集成

### API使用
- GitHub Contents API：上传文件
- GitHub Repository API：验证仓库访问权限
- Base64编码：处理二进制图片数据

### 安全考虑
- Token存储在localStorage中
- 支持HTTPS传输
- 文件类型和大小验证

## 故障排除

### 调试步骤
1. 打开浏览器开发者工具
2. 查看Console标签页的错误信息
3. 检查Network标签页的API请求
4. 使用测试页面验证各个功能模块

### 常见问题
1. **上传速度慢**：可能是网络问题或文件过大
2. **图片不显示**：检查仓库是否为公开仓库
3. **权限错误**：确保Token有repo权限

## 更新日志

### v1.0.0
- ✅ 基础拖拽上传功能
- ✅ 按钮选择上传功能
- ✅ 粘贴上传功能
- ✅ 自动重试机制
- ✅ 进度显示
- ✅ 错误处理
- ✅ 批量上传支持
- ✅ 完整测试套件

## 贡献指南

如需改进或扩展功能，请：
1. Fork项目仓库
2. 创建功能分支
3. 提交Pull Request
4. 确保通过所有测试

## 许可证

本功能基于项目原有许可证开源。
