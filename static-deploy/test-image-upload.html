<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片上传功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #002FA7;
            border-bottom: 2px solid #002FA7;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-item {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .test-item p {
            margin: 5px 0;
            color: #666;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            display: inline-block;
            margin-top: 10px;
        }
        .status.pass {
            background: #d4edda;
            color: #155724;
        }
        .status.fail {
            background: #f8d7da;
            color: #721c24;
        }
        .status.pending {
            background: #fff3cd;
            color: #856404;
        }
        .config-section {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .config-section h4 {
            margin-top: 0;
            color: #1976d2;
        }
        .config-item {
            margin-bottom: 10px;
        }
        .config-item label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .config-item input {
            width: 300px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        button {
            background: #002FA7;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0033B8;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .drop-zone {
            border: 2px dashed #002FA7;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            background: #f8f9ff;
            transition: all 0.3s ease;
        }
        .drop-zone.dragover {
            background: #e3f2fd;
            border-color: #0033B8;
        }
        .results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        .result-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
            border-left: 4px solid #002FA7;
            background: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 class="test-title">图片上传功能测试页面</h2>
        
        <div class="config-section">
            <h4>GitHub配置测试</h4>
            <div class="config-item">
                <label>Token:</label>
                <input type="password" id="token" placeholder="GitHub Personal Access Token">
            </div>
            <div class="config-item">
                <label>Owner:</label>
                <input type="text" id="owner" placeholder="GitHub用户名或组织名">
            </div>
            <div class="config-item">
                <label>Repository:</label>
                <input type="text" id="repo" placeholder="仓库名">
            </div>
            <button onclick="testGitHubConfig()">测试GitHub连接</button>
            <button onclick="saveConfig()">保存配置</button>
        </div>

        <div class="test-item">
            <h4>1. 服务初始化测试</h4>
            <p>检查GitHub服务和图片上传服务是否正确初始化</p>
            <button onclick="testServiceInit()">运行测试</button>
            <div id="service-init-result" class="status pending">等待测试</div>
        </div>

        <div class="test-item">
            <h4>2. 文件验证测试</h4>
            <p>测试图片文件格式和大小验证功能</p>
            <input type="file" id="file-input" accept="image/*" onchange="testFileValidation(this.files[0])">
            <div id="file-validation-result" class="status pending">选择图片文件进行测试</div>
        </div>

        <div class="test-item">
            <h4>3. 拖拽上传测试</h4>
            <p>将图片文件拖拽到下方区域进行测试</p>
            <div class="drop-zone" id="drop-zone">
                拖拽图片文件到这里
            </div>
            <div id="drag-drop-result" class="status pending">等待拖拽测试</div>
        </div>

        <div class="test-item">
            <h4>4. 按钮上传测试</h4>
            <p>点击按钮选择图片进行上传测试</p>
            <button onclick="testButtonUpload()">选择图片上传</button>
            <div id="button-upload-result" class="status pending">等待上传测试</div>
        </div>

        <div class="test-item">
            <h4>5. 批量上传测试</h4>
            <p>选择多张图片进行批量上传测试</p>
            <input type="file" id="multiple-file-input" accept="image/*" multiple onchange="testMultipleUpload(this.files)">
            <div id="multiple-upload-result" class="status pending">选择多张图片进行测试</div>
        </div>

        <div class="results" id="test-results">
            <h4>测试结果日志</h4>
            <p>测试结果将显示在这里...</p>
        </div>
    </div>

    <script src="github-service.js"></script>
    <script src="image-upload-service.js"></script>
    <script>
        let githubService;
        let imageUploadService;
        
        // 初始化服务
        document.addEventListener('DOMContentLoaded', function() {
            githubService = new GitHubService();
            imageUploadService = new ImageUploadService(githubService);
            
            // 设置拖拽事件
            setupDragAndDrop();
            
            // 加载保存的配置
            loadSavedConfig();
            
            logResult('页面加载完成，服务已初始化');
        });

        function logResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const item = document.createElement('div');
            item.className = 'result-item';
            item.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(item);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = message;
        }

        function loadSavedConfig() {
            const config = localStorage.getItem('github-config');
            if (config) {
                const parsed = JSON.parse(config);
                document.getElementById('token').value = parsed.token || '';
                document.getElementById('owner').value = parsed.owner || '';
                document.getElementById('repo').value = parsed.repo || '';
                logResult('已加载保存的GitHub配置');
            }
        }

        function saveConfig() {
            const config = {
                token: document.getElementById('token').value,
                owner: document.getElementById('owner').value,
                repo: document.getElementById('repo').value
            };
            
            if (!config.token || !config.owner || !config.repo) {
                alert('请填写完整的配置信息');
                return;
            }
            
            localStorage.setItem('github-config', JSON.stringify(config));
            githubService.setConfig(config);
            logResult('GitHub配置已保存');
        }

        async function testGitHubConfig() {
            const config = {
                token: document.getElementById('token').value,
                owner: document.getElementById('owner').value,
                repo: document.getElementById('repo').value
            };
            
            if (!config.token || !config.owner || !config.repo) {
                alert('请先填写GitHub配置信息');
                return;
            }
            
            try {
                githubService.setConfig(config);
                const result = await githubService.testConnection();
                if (result.success) {
                    logResult(`GitHub连接成功: ${result.user}, ${result.repo}`, 'success');
                } else {
                    logResult(`GitHub连接失败: ${result.error}`, 'error');
                }
            } catch (error) {
                logResult(`GitHub连接测试失败: ${error.message}`, 'error');
            }
        }

        function testServiceInit() {
            try {
                if (typeof GitHubService === 'function' && typeof ImageUploadService === 'function') {
                    if (githubService && imageUploadService) {
                        updateStatus('service-init-result', 'pass', '服务初始化成功');
                        logResult('服务初始化测试通过', 'success');
                    } else {
                        updateStatus('service-init-result', 'fail', '服务实例创建失败');
                        logResult('服务实例创建失败', 'error');
                    }
                } else {
                    updateStatus('service-init-result', 'fail', '服务类未正确加载');
                    logResult('服务类未正确加载', 'error');
                }
            } catch (error) {
                updateStatus('service-init-result', 'fail', `初始化错误: ${error.message}`);
                logResult(`服务初始化错误: ${error.message}`, 'error');
            }
        }

        function testFileValidation(file) {
            if (!file) return;
            
            try {
                const validation = imageUploadService.validateImageFile(file);
                if (validation.isValid) {
                    updateStatus('file-validation-result', 'pass', `文件验证通过: ${validation.fileInfo.name} (${validation.fileInfo.sizeText})`);
                    logResult(`文件验证通过: ${file.name}`, 'success');
                } else {
                    updateStatus('file-validation-result', 'fail', `验证失败: ${validation.errors.join(', ')}`);
                    logResult(`文件验证失败: ${validation.errors.join(', ')}`, 'error');
                }
            } catch (error) {
                updateStatus('file-validation-result', 'fail', `验证错误: ${error.message}`);
                logResult(`文件验证错误: ${error.message}`, 'error');
            }
        }

        function setupDragAndDrop() {
            const dropZone = document.getElementById('drop-zone');
            
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, preventDefaults, false);
            });
            
            ['dragenter', 'dragover'].forEach(eventName => {
                dropZone.addEventListener(eventName, highlight, false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, unhighlight, false);
            });
            
            dropZone.addEventListener('drop', handleDrop, false);
        }

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function highlight(e) {
            document.getElementById('drop-zone').classList.add('dragover');
        }

        function unhighlight(e) {
            document.getElementById('drop-zone').classList.remove('dragover');
        }

        async function handleDrop(e) {
            const files = Array.from(e.dataTransfer.files).filter(file => file.type.startsWith('image/'));
            
            if (files.length === 0) {
                updateStatus('drag-drop-result', 'fail', '没有找到有效的图片文件');
                logResult('拖拽测试失败: 没有有效图片', 'error');
                return;
            }
            
            updateStatus('drag-drop-result', 'pass', `检测到 ${files.length} 个图片文件`);
            logResult(`拖拽测试成功: 检测到 ${files.length} 个图片文件`, 'success');
            
            // 如果配置了GitHub，尝试上传
            if (githubService.isConfigured()) {
                await testUpload(files[0], 'drag-drop-result');
            }
        }

        function testButtonUpload() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (file) {
                    await testUpload(file, 'button-upload-result');
                }
            };
            input.click();
        }

        async function testMultipleUpload(files) {
            const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'));
            
            if (imageFiles.length === 0) {
                updateStatus('multiple-upload-result', 'fail', '没有选择有效的图片文件');
                return;
            }
            
            updateStatus('multiple-upload-result', 'pending', `准备上传 ${imageFiles.length} 张图片...`);
            logResult(`开始批量上传测试: ${imageFiles.length} 张图片`);
            
            if (!githubService.isConfigured()) {
                updateStatus('multiple-upload-result', 'fail', 'GitHub未配置');
                logResult('批量上传失败: GitHub未配置', 'error');
                return;
            }
            
            try {
                const { results, errors } = await imageUploadService.uploadMultipleImages(imageFiles, {
                    onProgress: (current, total, message) => {
                        updateStatus('multiple-upload-result', 'pending', `${message} (${current + 1}/${total})`);
                    },
                    onSuccess: (result) => {
                        logResult(`上传成功: ${result.fileName}`, 'success');
                    },
                    onError: (error, file) => {
                        logResult(`上传失败: ${file.name} - ${error.message}`, 'error');
                    }
                });
                
                if (results.length > 0) {
                    updateStatus('multiple-upload-result', 'pass', `成功上传 ${results.length} 张图片`);
                    logResult(`批量上传完成: 成功 ${results.length} 张，失败 ${errors.length} 张`, 'success');
                } else {
                    updateStatus('multiple-upload-result', 'fail', '所有图片上传失败');
                    logResult('批量上传失败: 所有图片都上传失败', 'error');
                }
            } catch (error) {
                updateStatus('multiple-upload-result', 'fail', `上传错误: ${error.message}`);
                logResult(`批量上传错误: ${error.message}`, 'error');
            }
        }

        async function testUpload(file, statusElementId) {
            if (!githubService.isConfigured()) {
                updateStatus(statusElementId, 'fail', 'GitHub未配置');
                logResult('上传失败: GitHub未配置', 'error');
                return;
            }
            
            updateStatus(statusElementId, 'pending', '正在上传...');
            logResult(`开始上传: ${file.name}`);
            
            try {
                const result = await imageUploadService.uploadSingleImage(file, {
                    onProgress: (percent, message) => {
                        updateStatus(statusElementId, 'pending', `${message} (${percent}%)`);
                    }
                });
                
                updateStatus(statusElementId, 'pass', `上传成功: ${result.fileName}`);
                logResult(`上传成功: ${file.name} -> ${result.url}`, 'success');
                logResult(`Markdown链接: ${result.markdownLink}`, 'info');
                
            } catch (error) {
                updateStatus(statusElementId, 'fail', `上传失败: ${error.message}`);
                logResult(`上传失败: ${file.name} - ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
