/**
 * 图片上传服务类
 * 专门处理图片上传到GitHub图床的功能
 */
class ImageUploadService {
  constructor(githubService) {
    this.githubService = githubService
    this.uploadQueue = []
    this.isUploading = false
  }

  /**
   * 验证图片文件
   * @param {File} file - 文件对象
   */
  validateImageFile(file) {
    const errors = []

    // 检查文件是否存在
    if (!file) {
      errors.push('文件不存在')
      return { isValid: false, errors }
    }

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      errors.push('文件必须是图片格式')
    }

    // 检查文件大小 (最大10MB)
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      errors.push(`图片大小不能超过${Math.round(maxSize / 1024 / 1024)}MB，当前大小：${Math.round(file.size / 1024 / 1024 * 100) / 100}MB`)
    }

    // 检查文件大小最小值 (最小1KB)
    const minSize = 1024
    if (file.size < minSize) {
      errors.push('图片文件太小，可能已损坏')
    }

    // 检查支持的图片格式
    const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
    if (!supportedTypes.includes(file.type)) {
      errors.push(`不支持的图片格式：${file.type}，请使用 JPEG、PNG、GIF、WebP 或 SVG 格式`)
    }

    // 检查文件名
    if (!file.name || file.name.trim() === '') {
      errors.push('文件名不能为空')
    }

    return {
      isValid: errors.length === 0,
      errors,
      fileInfo: {
        name: file.name,
        size: file.size,
        type: file.type,
        sizeText: this.formatFileSize(file.size)
      }
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 上传单张图片
   * @param {File} file - 图片文件
   * @param {Object} options - 上传选项
   */
  async uploadSingleImage(file, options = {}) {
    const { onProgress, onSuccess, onError, maxRetries = 3 } = options
    let retryCount = 0

    while (retryCount <= maxRetries) {
      try {
        // 验证文件
        const validation = this.validateImageFile(file)
        if (!validation.isValid) {
          throw new Error(validation.errors.join(', '))
        }

        // 开始上传进度
        if (onProgress) onProgress(0, retryCount > 0 ? `重试上传 (${retryCount}/${maxRetries})...` : '开始上传...')

        // 检查GitHub服务配置
        if (!this.githubService.isConfigured()) {
          throw new Error('GitHub未配置，请先配置GitHub信息')
        }

        if (onProgress) onProgress(20, '正在上传到GitHub...')

        // 上传到GitHub
        const result = await this.githubService.uploadImage(file, options.customPath)

        if (onProgress) onProgress(100, '上传完成')

        // 生成markdown链接
        const markdownLink = this.generateMarkdownLink(result.fileName, result.url)

        const uploadResult = {
          ...result,
          markdownLink,
          file
        }

        if (onSuccess) onSuccess(uploadResult)

        return uploadResult

      } catch (error) {
        retryCount++
        console.error(`图片上传失败 (尝试 ${retryCount}/${maxRetries + 1}):`, error)

        if (retryCount > maxRetries) {
          // 所有重试都失败了
          const finalError = new Error(`上传失败: ${error.message}${maxRetries > 0 ? ` (已重试 ${maxRetries} 次)` : ''}`)
          if (onError) onError(finalError)
          throw finalError
        }

        // 等待一段时间后重试
        if (onProgress) onProgress(0, `上传失败，${2 ** retryCount}秒后重试...`)
        await this.delay(2 ** retryCount * 1000) // 指数退避
      }
    }
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 批量上传图片
   * @param {File[]} files - 图片文件数组
   * @param {Object} options - 上传选项
   */
  async uploadMultipleImages(files, options = {}) {
    const { onProgress, onSuccess, onError, onComplete } = options
    const results = []
    const errors = []
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      
      try {
        if (onProgress) {
          onProgress(i, files.length, `正在上传 ${file.name}...`)
        }
        
        const result = await this.uploadSingleImage(file, {
          onProgress: (percent, message) => {
            if (onProgress) {
              onProgress(i, files.length, message, percent)
            }
          }
        })
        
        results.push(result)
        if (onSuccess) onSuccess(result, i, files.length)
        
      } catch (error) {
        errors.push({ file, error })
        if (onError) onError(error, file, i)
      }
    }
    
    if (onComplete) {
      onComplete(results, errors)
    }
    
    return { results, errors }
  }

  /**
   * 处理拖拽文件
   * @param {DataTransfer} dataTransfer - 拖拽数据
   */
  async handleDropFiles(dataTransfer) {
    const files = Array.from(dataTransfer.files).filter(file => 
      file.type.startsWith('image/')
    )
    
    if (files.length === 0) {
      throw new Error('没有找到有效的图片文件')
    }
    
    return files
  }

  /**
   * 处理粘贴文件
   * @param {ClipboardEvent} clipboardEvent - 粘贴事件
   */
  async handlePasteFiles(clipboardEvent) {
    const items = Array.from(clipboardEvent.clipboardData.items)
    const files = []
    
    for (const item of items) {
      if (item.kind === 'file' && item.type.startsWith('image/')) {
        files.push(item.getAsFile())
      }
    }
    
    if (files.length === 0) {
      throw new Error('剪贴板中没有找到图片文件')
    }
    
    return files
  }

  /**
   * 生成markdown图片链接
   * @param {string} fileName - 文件名
   * @param {string} url - 图片URL
   */
  generateMarkdownLink(fileName, url) {
    // 清理文件名作为alt文本
    const altText = fileName.replace(/\.[^/.]+$/, "").replace(/[_-]/g, ' ')
    return `![${altText}](${url})`
  }

  /**
   * 压缩图片（可选功能）
   * @param {File} file - 原始文件
   * @param {Object} options - 压缩选项
   */
  async compressImage(file, options = {}) {
    const { quality = 0.8, maxWidth = 1920, maxHeight = 1080 } = options
    
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()
      
      img.onload = () => {
        // 计算新尺寸
        let { width, height } = img
        
        if (width > maxWidth) {
          height = (height * maxWidth) / width
          width = maxWidth
        }
        
        if (height > maxHeight) {
          width = (width * maxHeight) / height
          height = maxHeight
        }
        
        canvas.width = width
        canvas.height = height
        
        // 绘制压缩后的图片
        ctx.drawImage(img, 0, 0, width, height)
        
        canvas.toBlob(resolve, file.type, quality)
      }
      
      img.src = URL.createObjectURL(file)
    })
  }

  /**
   * 获取图片信息
   * @param {File} file - 图片文件
   */
  async getImageInfo(file) {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height,
          size: file.size,
          type: file.type,
          name: file.name
        })
      }
      img.src = URL.createObjectURL(file)
    })
  }

  /**
   * 清理临时URL
   * @param {string} url - 临时URL
   */
  cleanupTempUrl(url) {
    if (url && url.startsWith('blob:')) {
      URL.revokeObjectURL(url)
    }
  }
}

// 导出服务类
window.ImageUploadService = ImageUploadService
